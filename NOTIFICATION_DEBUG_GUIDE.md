# 🔍 Comprehensive Notification Debug Guide

## **Current Status:**
✅ FCM tokens saved and matching on both devices  
❌ Notifications still not appearing when messages are sent

---

## **Step-by-Step Debugging Process:**

### **Step 1: Test Basic Notification Display**
**On your phone (student side):**
1. Open app → Menu → "🧪 Test Notifications" → "🎯 Test Direct Notification"
2. **Expected**: You should see a test notification appear immediately
3. **If this works**: Notification display system is working
4. **If this fails**: There's a basic notification permission/channel issue

### **Step 2: Check Message Sending Logs**
**When you send a message from phone to emulator:**

**Look for these logs in Android Studio (filter by "NotificationManager"):**
```
🔔 ===== MESSAGE NOTIFICATION START =====
📧 Message content: [your message]
👤 Sender ID: [student_user_id]
👤 Receiver ID: [company_user_id]
🎫 Receiver token result: FOUND ([token]...)
🚀 About to call sendFcmMessage...
📤 FCM Message JSON: [json]
🔍 DETAILED TOKEN COMPARISON:
   Target token length: [number]
   Current token length: [number]
   Tokens equal: true/false
✅ MATCH CONFIRMED - This notification is for THIS device
🚀 Calling showLocalNotification...
✅ showLocalNotification call completed
🔔 ===== MESSAGE NOTIFICATION END =====
```

### **Step 3: Identify the Issue**

**Scenario A: No logs appear**
- Message sending isn't triggering notifications
- Check if `MessageRepository.sendMessage()` is being called

**Scenario B: "Receiver token result: NOT FOUND"**
- FCM token not found for receiver
- Check if company user has token saved

**Scenario C: "Tokens equal: false"**
- Token mismatch between target and current device
- The notification is being sent to wrong device

**Scenario D: "NO MATCH - This notification is for ANOTHER device"**
- Notification targeting is working correctly
- The notification is meant for the other device (emulator)

**Scenario E: All logs appear but no notification**
- `showLocalNotification` is being called but failing
- Check notification permissions/channels

---

## **Debugging Commands:**

### **Check Logs:**
```bash
# Filter for notification-related logs
adb logcat | grep -E "(NotificationManager|MessageRepo|DIRECT_TEST|FCM_)"
```

### **Check Notification Permissions:**
- Go to Android Settings → Apps → CareerWorx → Notifications
- Ensure all notification categories are enabled

### **Check Notification Channels:**
- Look for logs: "🔧 Creating notification channels..."
- Ensure channels are created successfully

---

## **Expected Log Flow for Working Notifications:**

### **When Student Sends Message to Company:**
1. **Student device logs**: Message sent, notification processing starts
2. **Company device logs**: Token comparison, notification display
3. **Result**: Only company device shows notification

### **When Company Sends Message to Student:**
1. **Company device logs**: Message sent, notification processing starts  
2. **Student device logs**: Token comparison, notification display
3. **Result**: Only student device shows notification

---

## **Quick Tests to Run:**

1. **🎯 Test Direct Notification** (both devices)
   - Verifies basic notification display works

2. **🔍 Debug FCM Tokens** (both devices)  
   - Verifies tokens are saved and matching

3. **Send test message** and watch logs
   - Identifies where the process breaks

4. **Check notification settings** on both devices
   - Ensure permissions are granted

---

## **Common Issues & Solutions:**

### **Issue: Direct test works, but message notifications don't**
- **Cause**: Token comparison logic issue
- **Solution**: Check if receiver tokens are being found correctly

### **Issue: Logs show "MATCH CONFIRMED" but no notification**
- **Cause**: Notification channel/permission issue
- **Solution**: Check notification settings, recreate channels

### **Issue: "Receiver token result: NOT FOUND"**
- **Cause**: FCM token not saved for receiver
- **Solution**: Force save token on receiver device

### **Issue: Notifications appear on wrong device**
- **Cause**: Token mismatch or user ID confusion
- **Solution**: Verify user IDs and token mapping

---

**Run the "🎯 Test Direct Notification" first and tell me the results!**

This will help us identify if the issue is:
- Basic notification display ❌
- Token comparison logic ❌  
- Message triggering ❌
- Or something else ❌
