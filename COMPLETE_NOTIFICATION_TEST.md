# 🔔 Complete Notification System Test Guide

## **Student Notifications (Phone):**
1. 🔔 **New job posting** (when company posts a job)
2. 📋 **Application reviewed** (when company views their application)
3. ✅❌ **Application accepted/rejected** (status changes)
4. 💬 **New messages** (from company)

## **Company Notifications (Emulator):**
1. 📋 **New application** (when student applies)
2. 💬 **New messages** (from student)

---

## **Test Scenarios:**

### 🧪 **Test 1: Job Application Flow**
1. **Company (emulator)**: Post a new job
2. **Student (phone)**: Should get notification "New Job: Software Developer"
3. **Student (phone)**: Apply for the job
4. **Company (emulator)**: Should get notification "<PERSON> applied for Software Developer"
5. **Company (emulator)**: Click on the application to view details
6. **Student (phone)**: Should get notification "Company Name has reviewed your application for Software Developer"

### 🧪 **Test 2: Application Status Changes**
1. **Company (emulator)**: Accept or reject the application
2. **Student (phone)**: Should get notification "Congratulations! Your application for Software Developer at Company Name has been accepted" (or rejected)

### 🧪 **Test 3: Message Flow**
1. **Company (emulator)**: Send message to student
2. **Student (phone)**: Should get notification "New message from Company Name"
3. **Student (phone)**: Reply to the message
4. **Company (emulator)**: Should get notification "New message from Student Name"

---

## **Expected Logs:**

### For New Application:
```
🔔 sendNewApplicationNotification called!
📋 Application ID: [app_id]
🏢 Company ID: [company_id]
👤 Candidate: John Doe
💼 Job: Software Developer
✅ New application notification sent to company
```

### For Application Reviewed:
```
🔔 sendApplicationReviewedNotification called!
👤 Candidate ID: [candidate_id]
💼 Job: Software Developer
🏢 Company: Company Name
✅ Application reviewed notification sent to candidate
```

### For Application Status:
```
Application status notification sent to candidate: [candidate_id]
```

### For Messages:
```
🔔 sendNewMessageNotification called!
📧 Message: Hello there!
✅ Message notification sent to user: [receiver_id]
```

---

## **Quick Test Checklist:**

### ✅ **Student Side (Phone):**
- [ ] Gets notification when company posts job
- [ ] Gets notification when company views application
- [ ] Gets notification when application is accepted/rejected
- [ ] Gets notification when company sends message

### ✅ **Company Side (Emulator):**
- [ ] Gets notification when student applies for job
- [ ] Gets notification when student sends message

---

## **Troubleshooting:**

### If notifications don't appear:
1. **Check logs** for "🔔" messages
2. **Check FCM tokens** - look for "🎫 Token: NOT FOUND" errors
3. **Check notification permissions** on both devices
4. **Check notification channels** - look for "🔧 Creating notification channels..."

### Common Issues:
- **"Token not found"**: FCM tokens not saved to Firestore
- **"No Channel found"**: Notification channels not created (should auto-create now)
- **Wrong device gets notification**: Expected for local testing (FCM will fix this in production)

---

## **Ready to Test?**

1. **Build and install** the updated app on both devices
2. **Follow Test Scenarios** above in order
3. **Check logs** in Android Studio for detailed debugging
4. **Verify notifications** appear on correct devices

**All notification types should now work!** 🚀
