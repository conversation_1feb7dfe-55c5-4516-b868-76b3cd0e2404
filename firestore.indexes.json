{"indexes": [{"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "applieddate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "candidateId", "order": "ASCENDING"}, {"fieldPath": "applieddate", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "postedDate", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "postedDate", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "candidateId", "order": "ASCENDING"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}], "fieldOverrides": []}