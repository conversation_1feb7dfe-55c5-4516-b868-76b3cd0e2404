{"indexes": [{"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "postedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "postedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "title", "order": "ASCENDING"}, {"fieldPath": "location", "order": "ASCENDING"}]}, {"collectionGroup": "jobs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "postedDate", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "jobId", "order": "ASCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "applicantId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "applications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "appliedDate", "order": "DESCENDING"}, {"fieldPath": "__name__", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "educationLevel", "order": "ASCENDING"}, {"fieldPath": "yearsExperience", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "skills", "arrayConfig": "CONTAINS"}, {"fieldPath": "yearsExperience", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "willingToRelocate", "order": "ASCENDING"}, {"fieldPath": "yearsExperience", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "educationLevel", "order": "ASCENDING"}, {"fieldPath": "willingToRelocate", "order": "ASCENDING"}, {"fieldPath": "yearsExperience", "order": "ASCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "candidateId", "order": "ASCENDING"}, {"fieldPath": "lastMessageTime", "order": "DESCENDING"}]}, {"collectionGroup": "conversations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "applicationId", "order": "ASCENDING"}]}], "fieldOverrides": []}