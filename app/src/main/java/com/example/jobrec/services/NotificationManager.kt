package com.example.jobrec.services
import android.util.Log
import com.example.jobrec.services.FCMService
import com.example.jobrec.models.Conversation
import com.example.jobrec.Job
import com.example.jobrec.models.Message
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await
import org.json.JSONObject
// Removed unused HTTP imports - using Cloud Functions instead
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import com.example.jobrec.R
import com.example.jobrec.CareerWorxApp
class NotificationManager(private val context: Context? = null) {
    private val TAG = "NotificationManager"
    private val db = FirebaseFirestore.getInstance()
    // Using FCM V1 API (your project has Legacy API disabled)
    private val PROJECT_ID = "careerworx-f5bc6" // Your Firebase project ID
    private val FCM_V1_URL = "https://fcm.googleapis.com/v1/projects/$PROJECT_ID/messages:send"
    companion object {
        const val TOPIC_ALL_JOBS = "all_jobs"
        const val TOPIC_JOB_CATEGORY_PREFIX = "job_category_"
        const val TOPIC_JOB_SPECIALIZATION_PREFIX = "job_specialization_"
    }
    suspend fun subscribeToTopic(topic: String) {
        try {
            FirebaseMessaging.getInstance().subscribeToTopic(topic).await()
            android.util.Log.d(TAG, "Subscribed to topic: $topic")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error subscribing to topic: $topic", e)
            throw e
        }
    }
    suspend fun unsubscribeFromTopic(topic: String) {
        try {
            FirebaseMessaging.getInstance().unsubscribeFromTopic(topic).await()
            android.util.Log.d(TAG, "Unsubscribed from topic: $topic")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "Error unsubscribing from topic: $topic", e)
            throw e
        }
    }
    suspend fun sendNewJobNotification(job: Job) {
        withContext(Dispatchers.IO) {
            try {
                val topics = mutableListOf<String>()
                topics.add(TOPIC_ALL_JOBS)
                if (job.jobField.isNotEmpty()) {
                    val categoryTopic = TOPIC_JOB_CATEGORY_PREFIX + job.jobField.lowercase().replace(" ", "_")
                    topics.add(categoryTopic)
                }
                if (job.specialization.isNotEmpty()) {
                    val specializationTopic = TOPIC_JOB_SPECIALIZATION_PREFIX + job.specialization.lowercase().replace(" ", "_")
                    topics.add(specializationTopic)
                }
                for (topic in topics) {
                    val message = JSONObject().apply {
                        put("to", "/topics/$topic")
                        put("data", JSONObject().apply {
                            put("type", "job")
                            put("jobId", job.id)
                            put("title", "New Job: ${job.title}")
                            put("body", "${job.title} at ${job.companyName}")
                        })
                        put("notification", JSONObject().apply {
                            put("title", "New Job: ${job.title}")
                            put("body", "${job.title} at ${job.companyName}")
                            put("sound", "default")
                            put("priority", "high")
                            put("android_channel_id", FCMService.CHANNEL_ID_JOBS)
                            put("tag", "job_notification")
                        })
                    }
                    sendFcmMessage(message.toString())
                }
                android.util.Log.d(TAG, "Job notification sent to ${topics.size} topics")
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error sending job notification", e)
            }
        }
    }
    suspend fun sendNewMessageNotification(message: Message, conversation: Conversation) {
        withContext(Dispatchers.IO) {
            try {
                android.util.Log.d(TAG, "🔔 sendNewMessageNotification called!")
                android.util.Log.d(TAG, "📧 Message: ${message.content}")
                android.util.Log.d(TAG, "👤 Receiver ID: ${message.receiverId}")

                val receiverToken = getReceiverToken(message.receiverId)
                android.util.Log.d(TAG, "🎫 Receiver token: ${if (receiverToken.isNotEmpty()) "Found" else "NOT FOUND"}")

                if (receiverToken.isNotEmpty()) {
                    val fcmMessage = JSONObject().apply {
                        put("to", receiverToken)
                        put("data", JSONObject().apply {
                            put("type", "message")
                            put("conversationId", conversation.id)
                            put("title", "New message from ${if (message.senderId == conversation.companyId) conversation.companyName else conversation.candidateName}")
                            put("body", message.content)
                        })
                        put("notification", JSONObject().apply {
                            put("title", "New message from ${if (message.senderId == conversation.companyId) conversation.companyName else conversation.candidateName}")
                            put("body", message.content)
                            put("sound", "default")
                            put("priority", "high")
                            put("android_channel_id", FCMService.CHANNEL_ID_MESSAGES)
                            put("tag", "message_notification")
                        })
                    }
                    android.util.Log.d(TAG, "🚀 About to call sendFcmMessage...")
                    sendFcmMessage(fcmMessage.toString())
                    android.util.Log.d(TAG, "✅ Message notification sent to user: ${message.receiverId}")
                } else {
                    android.util.Log.e(TAG, "❌ Receiver token not found for user: ${message.receiverId}")
                    android.util.Log.e(TAG, "❌ This means FCM token is not saved in Firestore!")
                }
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error sending message notification", e)
            }
        }
    }

    suspend fun sendNewApplicationNotification(applicationId: String, companyId: String, candidateName: String, jobTitle: String) {
        withContext(Dispatchers.IO) {
            try {
                android.util.Log.d(TAG, "🔔 sendNewApplicationNotification called!")
                android.util.Log.d(TAG, "📋 Application ID: $applicationId")
                android.util.Log.d(TAG, "🏢 Company ID: $companyId")
                android.util.Log.d(TAG, "👤 Candidate: $candidateName")
                android.util.Log.d(TAG, "💼 Job: $jobTitle")

                val companyToken = getReceiverToken(companyId)
                android.util.Log.d(TAG, "🎫 Company token: ${if (companyToken.isNotEmpty()) "Found" else "NOT FOUND"}")

                if (companyToken.isNotEmpty()) {
                    val fcmMessage = JSONObject().apply {
                        put("to", companyToken)
                        put("data", JSONObject().apply {
                            put("type", "application")
                            put("applicationId", applicationId)
                            put("title", "New Application")
                            put("body", "$candidateName applied for $jobTitle")
                        })
                        put("notification", JSONObject().apply {
                            put("title", "New Application")
                            put("body", "$candidateName applied for $jobTitle")
                            put("sound", "default")
                            put("priority", "high")
                            put("android_channel_id", FCMService.CHANNEL_ID_APPLICATIONS)
                            put("tag", "new_application_notification")
                        })
                    }
                    android.util.Log.d(TAG, "🚀 About to call sendFcmMessage for new application...")
                    sendFcmMessage(fcmMessage.toString())
                    android.util.Log.d(TAG, "✅ New application notification sent to company: $companyId")
                } else {
                    android.util.Log.e(TAG, "❌ Company token not found for company: $companyId")
                    android.util.Log.e(TAG, "❌ This means FCM token is not saved in Firestore for company!")
                }
            } catch (e: Exception) {
                android.util.Log.e(TAG, "❌ Error sending new application notification", e)
            }
        }
    }

    suspend fun sendApplicationStatusNotification(applicationId: String, candidateId: String, status: String, jobTitle: String, companyName: String) {
        withContext(Dispatchers.IO) {
            try {
                val candidateToken = getReceiverToken(candidateId)
                if (candidateToken.isNotEmpty()) {
                    val statusMessage = when (status.lowercase()) {
                        "accepted" -> "Congratulations! Your application for $jobTitle at $companyName has been accepted."
                        "rejected" -> "Your application for $jobTitle at $companyName was not selected."
                        "shortlisted" -> "Great news! You've been shortlisted for $jobTitle at $companyName."
                        "interviewing" -> "You've been invited for an interview for $jobTitle at $companyName."
                        "offered" -> "Congratulations! You've received a job offer for $jobTitle at $companyName."
                        else -> "Your application status for $jobTitle at $companyName has been updated to $status."
                    }

                    val fcmMessage = JSONObject().apply {
                        put("to", candidateToken)
                        put("data", JSONObject().apply {
                            put("type", "application")
                            put("applicationId", applicationId)
                            put("title", "Application Update")
                            put("body", statusMessage)
                        })
                        put("notification", JSONObject().apply {
                            put("title", "Application Update")
                            put("body", statusMessage)
                            put("sound", "default")
                            put("priority", "high")
                            put("android_channel_id", FCMService.CHANNEL_ID_APPLICATIONS)
                            put("tag", "application_notification")
                        })
                    }
                    sendFcmMessage(fcmMessage.toString())
                    android.util.Log.d(TAG, "Application status notification sent to candidate: $candidateId")
                } else {
                    android.util.Log.d(TAG, "Candidate token not found for user: $candidateId")
                }
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error sending application status notification", e)
            }
        }
    }

    suspend fun sendProfileViewNotification(candidateId: String, companyName: String) {
        withContext(Dispatchers.IO) {
            try {
                val candidateToken = getReceiverToken(candidateId)
                if (candidateToken.isNotEmpty()) {
                    val fcmMessage = JSONObject().apply {
                        put("to", candidateToken)
                        put("data", JSONObject().apply {
                            put("type", "profile_view")
                            put("title", "Profile Viewed")
                            put("body", "$companyName has viewed your profile")
                        })
                        put("notification", JSONObject().apply {
                            put("title", "Profile Viewed")
                            put("body", "$companyName has viewed your profile")
                            put("sound", "default")
                            put("priority", "default")
                            put("android_channel_id", FCMService.CHANNEL_ID_PROFILE_VIEWS)
                            put("tag", "profile_view_notification")
                        })
                    }
                    sendFcmMessage(fcmMessage.toString())
                    android.util.Log.d(TAG, "Profile view notification sent to candidate: $candidateId")
                } else {
                    android.util.Log.d(TAG, "Candidate token not found for user: $candidateId")
                }
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error sending profile view notification", e)
            }
        }
    }

    suspend fun sendCvReviewNotification(candidateId: String, companyName: String) {
        withContext(Dispatchers.IO) {
            try {
                val candidateToken = getReceiverToken(candidateId)
                if (candidateToken.isNotEmpty()) {
                    val fcmMessage = JSONObject().apply {
                        put("to", candidateToken)
                        put("data", JSONObject().apply {
                            put("type", "cv_review")
                            put("title", "CV Reviewed")
                            put("body", "$companyName has reviewed your CV")
                        })
                        put("notification", JSONObject().apply {
                            put("title", "CV Reviewed")
                            put("body", "$companyName has reviewed your CV")
                            put("sound", "default")
                            put("priority", "default")
                            put("android_channel_id", FCMService.CHANNEL_ID_PROFILE_VIEWS)
                            put("tag", "cv_review_notification")
                        })
                    }
                    sendFcmMessage(fcmMessage.toString())
                    android.util.Log.d(TAG, "CV review notification sent to candidate: $candidateId")
                } else {
                    android.util.Log.d(TAG, "Candidate token not found for user: $candidateId")
                }
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error sending CV review notification", e)
            }
        }
    }
    private suspend fun getReceiverToken(userId: String): String {
        return withContext(Dispatchers.IO) {
            try {
                val userDoc = db.collection("users").document(userId).get().await()
                if (userDoc.exists() && userDoc.contains("fcmToken")) {
                    return@withContext userDoc.getString("fcmToken") ?: ""
                }
                val companyQuery = db.collection("companies")
                    .whereEqualTo("userId", userId)
                    .get()
                    .await()
                if (!companyQuery.isEmpty) {
                    val companyDoc = companyQuery.documents[0]
                    if (companyDoc.contains("fcmToken")) {
                        return@withContext companyDoc.getString("fcmToken") ?: ""
                    }
                }
                val directCompanyDoc = db.collection("companies").document(userId).get().await()
                if (directCompanyDoc.exists() && directCompanyDoc.contains("fcmToken")) {
                    return@withContext directCompanyDoc.getString("fcmToken") ?: ""
                }
                return@withContext ""
            } catch (e: Exception) {
                android.util.Log.e(TAG, "Error getting receiver token", e)
                return@withContext ""
            }
        }
    }
    private suspend fun sendFcmMessage(messageJson: String) {
        withContext(Dispatchers.IO) {
            try {
                android.util.Log.d(TAG, "🔔 Processing notification: $messageJson")

                // Parse the message to show local notification for immediate testing
                val jsonObject = JSONObject(messageJson)
                val dataObject = if (jsonObject.has("data")) jsonObject.getJSONObject("data") else JSONObject()
                val notificationObject = if (jsonObject.has("notification")) jsonObject.getJSONObject("notification") else JSONObject()

                val title = if (notificationObject.has("title")) notificationObject.getString("title") else "CareerWorx"
                val body = if (notificationObject.has("body")) notificationObject.getString("body") else "New notification"
                val type = if (dataObject.has("type")) dataObject.getString("type") else "general"

                android.util.Log.d(TAG, "✅ NOTIFICATION READY: $title - $body")
                android.util.Log.d(TAG, "📱 Type: $type")

                // Show local notification using application context (for immediate testing)
                val appContext = context ?: CareerWorxApp.instance

                // For testing: show all notifications (in production, FCM handles targeting)
                android.util.Log.d(TAG, "✅ Showing local notification for testing")
                showLocalNotification(appContext, title, body, type)

                android.util.Log.d(TAG, "🚀 For production: Implement FCM V1 API with service account authentication")
                android.util.Log.d(TAG, "📖 Your project uses FCM V1 API (Legacy API is disabled)")

            } catch (e: Exception) {
                android.util.Log.e(TAG, "❌ Error processing notification", e)
            }
        }
    }



    private fun showLocalNotification(context: Context, title: String, body: String, type: String) {
        try {
            android.util.Log.d(TAG, "🔔 showLocalNotification called!")
            android.util.Log.d(TAG, "📱 Title: $title")
            android.util.Log.d(TAG, "📝 Body: $body")
            android.util.Log.d(TAG, "🏷️ Type: $type")

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Create notification channels if they don't exist
            createNotificationChannels(context, notificationManager)

            // Create appropriate intent based on notification type
            val intent = when (type) {
                "message" -> Intent(context, com.example.jobrec.ConversationsActivity::class.java)
                "job" -> Intent(context, com.example.jobrec.SearchActivity::class.java)
                "application" -> Intent(context, com.example.jobrec.MyApplicationsActivity::class.java)
                else -> Intent(context, com.example.jobrec.HomeActivity::class.java)
            }

            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP

            val pendingIntent = PendingIntent.getActivity(
                context,
                System.currentTimeMillis().toInt(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val channelId = when (type) {
                "message" -> FCMService.CHANNEL_ID_MESSAGES
                "job" -> FCMService.CHANNEL_ID_JOBS
                "application" -> FCMService.CHANNEL_ID_APPLICATIONS
                else -> FCMService.CHANNEL_ID_JOBS
            }

            val notification = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL)
                .build()

            val notificationId = System.currentTimeMillis().toInt()
            android.util.Log.d(TAG, "🚀 About to show notification with ID: $notificationId")
            notificationManager.notify(notificationId, notification)
            android.util.Log.d(TAG, "✅ Local notification shown: $title")
            android.util.Log.d(TAG, "📱 Check your notification panel now!")

        } catch (e: Exception) {
            android.util.Log.e(TAG, "❌ Error showing local notification", e)
            e.printStackTrace()
        }
    }

    private fun createNotificationChannels(context: Context, notificationManager: NotificationManager) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            android.util.Log.d(TAG, "🔧 Creating notification channels...")

            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)

            // Job notifications channel
            val jobChannel = NotificationChannel(
                FCMService.CHANNEL_ID_JOBS,
                FCMService.CHANNEL_NAME_JOBS,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for new job opportunities"
                enableLights(true)
                enableVibration(true)
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                setShowBadge(true)
                setSound(defaultSoundUri, null)
            }

            // Message notifications channel
            val messageChannel = NotificationChannel(
                FCMService.CHANNEL_ID_MESSAGES,
                FCMService.CHANNEL_NAME_MESSAGES,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for new messages"
                enableLights(true)
                enableVibration(true)
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                setShowBadge(true)
                setSound(defaultSoundUri, null)
            }

            // Application notifications channel
            val applicationChannel = NotificationChannel(
                FCMService.CHANNEL_ID_APPLICATIONS,
                FCMService.CHANNEL_NAME_APPLICATIONS,
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for application status updates"
                enableLights(true)
                enableVibration(true)
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                setShowBadge(true)
                setSound(defaultSoundUri, null)
            }

            // Profile view notifications channel
            val profileViewChannel = NotificationChannel(
                FCMService.CHANNEL_ID_PROFILE_VIEWS,
                FCMService.CHANNEL_NAME_PROFILE_VIEWS,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Notifications for profile views and CV reviews"
                enableLights(true)
                enableVibration(false) // Less intrusive for profile views
                lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC
                setShowBadge(true)
                setSound(defaultSoundUri, null)
            }

            // Create all channels
            notificationManager.createNotificationChannel(jobChannel)
            notificationManager.createNotificationChannel(messageChannel)
            notificationManager.createNotificationChannel(applicationChannel)
            notificationManager.createNotificationChannel(profileViewChannel)

            android.util.Log.d(TAG, "✅ All notification channels created!")
        } else {
            android.util.Log.d(TAG, "📱 Android version < O, no channels needed")
        }
    }
}
