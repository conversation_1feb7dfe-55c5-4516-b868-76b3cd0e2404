package com.example.jobrec
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.button.MaterialButton
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.example.jobrec.ai.JobMatchingRepository
import com.example.jobrec.chatbot.ChatbotHelper
import com.example.jobrec.databinding.ActivityHomeBinding
import com.example.jobrec.utils.NotificationHelper
import com.example.jobrec.utils.NotificationTester
import kotlinx.coroutines.launch
class HomeActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "HomeActivity"
    }
    private lateinit var binding: ActivityHomeBinding
    private lateinit var auth: FirebaseAuth
    private lateinit var db: FirebaseFirestore
    private var userId: String? = null
    private lateinit var recentJobsAdapter: RecentJobsAdapter
    private lateinit var recommendedJobsAdapter: JobsAdapter
    private lateinit var notificationHelper: NotificationHelper
    private lateinit var jobMatchingRepository: JobMatchingRepository
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityHomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        auth = FirebaseAuth.getInstance()
        db = FirebaseFirestore.getInstance()
        userId = auth.currentUser?.uid
        notificationHelper = NotificationHelper(this)
        notificationHelper.createNotificationChannel()
        jobMatchingRepository = JobMatchingRepository()
        initializeViews()
        setupClickListeners()
        setupBottomNavigation()
        setupSwipeRefresh()
        loadData()
    }
    private fun initializeViews() {
        val toolbar = findViewById<androidx.appcompat.widget.Toolbar>(R.id.toolbar)
        setSupportActionBar(toolbar)
        supportActionBar?.title = "Student Dashboard"
        recentJobsAdapter = RecentJobsAdapter { job ->
            navigateToJobDetails(job.id)
        }
        recommendedJobsAdapter = JobsAdapter { job ->
            navigateToJobDetails(job.id)
        }
        binding.recentJobsRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@HomeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = recentJobsAdapter
            addItemDecoration(SpacingItemDecoration(16))
        }
        binding.recommendedJobsRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@HomeActivity)
            adapter = recommendedJobsAdapter
            addItemDecoration(SpacingItemDecoration(16))
        }
        applyEntranceAnimations()
    }
    private fun applyEntranceAnimations() {
        val fadeIn = AnimationUtils.loadAnimation(this, android.R.anim.fade_in)
        val slideUp = AnimationUtils.loadAnimation(this, R.anim.slide_up)
        binding.welcomeText.startAnimation(fadeIn)
        binding.searchCard.startAnimation(slideUp)
        binding.myApplicationsCard.startAnimation(slideUp)
        binding.jobAlertsCard.startAnimation(slideUp)
    }
    private fun setupSwipeRefresh() {
        binding.swipeRefreshLayout.setColorSchemeColors(
            ContextCompat.getColor(this, R.color.primary),
            ContextCompat.getColor(this, R.color.accent),
            ContextCompat.getColor(this, R.color.primary_dark)
        )
        binding.swipeRefreshLayout.setOnRefreshListener {
            loadData()
        }
    }
    private fun setupClickListeners() {
        binding.searchCard.setOnClickListener {
            animateClick(binding.searchCard) {
                startActivity(Intent(this, SearchActivity::class.java))
            }
        }
        binding.myApplicationsCard.setOnClickListener {
            animateClick(binding.myApplicationsCard) {
                startActivity(Intent(this, MyApplicationsActivity::class.java))
            }
        }
        binding.jobAlertsCard.setOnClickListener {
            animateClick(binding.jobAlertsCard) {
                startActivity(Intent(this, ConversationsActivity::class.java))
            }
        }
        findViewById<View>(R.id.savedJobsCard).setOnClickListener {
            animateClick(findViewById(R.id.savedJobsCard)) {
                startActivity(Intent(this, SavedJobsActivity::class.java))
            }
        }
    }
    private fun logout() {
        val sharedPreferences = getSharedPreferences("JobRecPrefs", Context.MODE_PRIVATE)
        sharedPreferences.edit()
            .putBoolean("override_to_student", false)
            .remove("user_type")
            .remove("user_id")
            .apply()
        auth.signOut()
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    private fun animateClick(view: View, action: () -> Unit) {
        val bounceAnimation = AnimationUtils.loadAnimation(this, R.anim.bounce)
        view.startAnimation(bounceAnimation)
        bounceAnimation.setAnimationListener(object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation?) {}
            override fun onAnimationRepeat(animation: Animation?) {}
            override fun onAnimationEnd(animation: Animation?) {
                action()
            }
        })
    }
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_home -> true
                R.id.navigation_applications -> {
                    startActivity(Intent(this, MyApplicationsActivity::class.java))
                    false
                }
                R.id.navigation_profile -> {
                    startActivity(Intent(this, ProfileActivity::class.java))
                    false
                }
                R.id.navigation_ai_assistant -> {
                    startActivity(Intent(this, com.example.jobrec.chatbot.ChatbotActivity::class.java))
                    false
                }
                else -> false
            }
        }
    }
    private fun loadData() {
        loadUserData()
        loadRecentJobs()
        loadRecommendedJobs()
        loadStats()
    }
    private fun loadUserData() {
        val isDefaultStudent = intent.getBooleanExtra("isDefaultStudent", false)
        binding.returnToCompanyView.visibility = View.GONE
        val userId = intent.getStringExtra("userId") ?: FirebaseAuth.getInstance().currentUser?.uid
        Log.d(TAG, "Loading user data for userId: $userId")
        if (userId != null) {
            db.collection("users")
                .document(userId)
                .get()
                .addOnSuccessListener { document ->
                    Log.d(TAG, "Document exists: ${document.exists()}")
                    if (document != null && document.exists()) {
                        val name = document.getString("name")
                        Log.d(TAG, "Retrieved name: $name")
                        val displayName = if (!name.isNullOrEmpty()) {
                            name.trim()
                        } else {
                            Log.d(TAG, "Name is empty, using default name")
                            "Student"
                        }
                        binding.welcomeText.text = "Welcome back, $displayName!"
                    } else {
                        Log.d(TAG, "Document does not exist or is null")
                        binding.welcomeText.text = "Welcome back, Student!"
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error loading user data", e)
                    binding.welcomeText.text = "Welcome back, Student!"
                }
        } else {
            Log.d(TAG, "No user ID found")
            binding.welcomeText.text = "Welcome back, Student!"
        }
    }
    private fun loadStats() {
        userId?.let { uid ->
            db.collection("applications")
                .whereEqualTo("userId", uid)
                .get()
                .addOnSuccessListener { documents ->
                    binding.applicationsCount.text = documents.size().toString()
                    binding.applicationsCount.startAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_in))
                }
                .addOnFailureListener { e ->
                    Log.w(TAG, "Error loading applications count", e)
                }
            db.collection("savedJobs")
                .whereEqualTo("userId", uid)
                .get()
                .addOnSuccessListener { documents ->
                    binding.savedJobsCount.text = documents.size().toString()
                    binding.savedJobsCount.startAnimation(AnimationUtils.loadAnimation(this, android.R.anim.fade_in))
                }
                .addOnFailureListener { e ->
                    Log.w(TAG, "Error loading saved jobs count", e)
                }
        }
    }
    private fun loadRecentJobs() {
        Log.d(TAG, "Loading recent jobs with match percentages...")
        lifecycleScope.launch {
            try {
                // Get all jobs with match percentages
                val allJobsWithMatches = jobMatchingRepository.getJobsWithMatches(50)

                // Filter for active jobs and sort by posted date
                val recentJobs = allJobsWithMatches
                    .filter { it.status == "active" }
                    .sortedByDescending { it.postedDate.toDate() }
                    .take(10) // Limit to 10 most recent jobs

                Log.d(TAG, "Successfully loaded ${recentJobs.size} recent jobs with match percentages")
                recentJobsAdapter.submitList(recentJobs)
                binding.swipeRefreshLayout.isRefreshing = false
            } catch (e: Exception) {
                Log.e(TAG, "Error loading recent jobs with matches: ${e.message}", e)
                // Fallback to loading jobs without match percentages
                loadRecentJobsFallback()
            }
        }
    }

    private fun loadRecentJobsFallback() {
        Log.d(TAG, "Loading recent jobs fallback...")
        db.collection("jobs")
            .whereEqualTo("status", "active")
            .orderBy("postedDate", Query.Direction.DESCENDING)
            .limit(10)
            .get()
            .addOnSuccessListener { documents ->
                Log.d(TAG, "Found ${documents.size()} recent jobs")
                val jobs = documents.mapNotNull { doc ->
                    try {
                        doc.toObject(Job::class.java).copy(id = doc.id)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error converting document to Job object: ${e.message}")
                        null
                    }
                }
                Log.d(TAG, "Successfully mapped ${jobs.size} recent jobs")
                recentJobsAdapter.submitList(jobs)
                binding.swipeRefreshLayout.isRefreshing = false
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading recent jobs: ${e.message}", e)
                showError("Failed to load recent jobs: ${e.message}")
                binding.swipeRefreshLayout.isRefreshing = false
            }
    }
    private fun loadRecommendedJobs() {
        Log.d(TAG, "Loading AI-powered recommended jobs...")
        lifecycleScope.launch {
            try {
                val allJobsWithMatches = jobMatchingRepository.getJobsWithMatches(100) // Get more jobs to filter from
                // Filter for recommended jobs (50%+ match with much improved algorithm)
                val highMatchJobs = allJobsWithMatches.filter { job ->
                    job.matchPercentage >= 50
                }.take(10) // Limit to 10 high-match jobs

                Log.d(TAG, "Successfully loaded ${highMatchJobs.size} recommended jobs (50%+ match) from ${allJobsWithMatches.size} total jobs")
                recommendedJobsAdapter.submitList(highMatchJobs)

                // Update subtitle based on results
                updateRecommendedJobsSubtitle(highMatchJobs.size, true)

                // If no high-match jobs found, show empty state message
                if (highMatchJobs.isEmpty()) {
                    Log.d(TAG, "No high-match jobs (75%+) found")
                    updateRecommendedJobsSubtitle(0, true)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading AI-powered recommended jobs: ${e.message}", e)
                // Show empty state instead of fallback for recommended jobs
                updateRecommendedJobsSubtitle(0, false)
            }
        }
    }

    private fun loadFallbackRecommendedJobs() {
        Log.d(TAG, "Loading fallback recommended jobs...")
        db.collection("jobs")
            .whereEqualTo("status", "active")
            .orderBy("postedDate", Query.Direction.DESCENDING)
            .limit(20) // Get more jobs to potentially find some with matches
            .get()
            .addOnSuccessListener { documents ->
                Log.d(TAG, "Found ${documents.size()} fallback recommended jobs")
                val jobs = documents.mapNotNull { doc ->
                    try {
                        doc.toObject(Job::class.java).copy(id = doc.id)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error converting document to Job object: ${e.message}")
                        null
                    }
                }

                // Try to prioritize jobs with any match percentage > 0, then by posted date
                val sortedJobs = jobs.sortedWith(compareByDescending<Job> { it.matchPercentage }.thenByDescending { it.postedDate.toDate() })
                    .take(10)

                Log.d(TAG, "Successfully mapped ${sortedJobs.size} fallback recommended jobs")
                recommendedJobsAdapter.submitList(sortedJobs)

                // Update subtitle for fallback jobs
                updateRecommendedJobsSubtitle(sortedJobs.size, false)
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading fallback recommended jobs: ${e.message}", e)
                showError("Failed to load recommended jobs: ${e.message}")
            }
    }

    private fun updateRecommendedJobsSubtitle(jobCount: Int, isHighMatch: Boolean) {
        val subtitle = when {
            jobCount == 0 && isHighMatch -> "No matching jobs found at the moment"
            jobCount == 0 -> "No recommendations available at the moment"
            isHighMatch -> "Recommended jobs (50%+ compatibility) • $jobCount found"
            else -> "Recent job postings • $jobCount available"
        }
        binding.recommendedJobsSubtitle.text = subtitle
    }
    private fun navigateToJobDetails(jobId: String) {
        if (jobId.isBlank()) {
            Log.e(TAG, "Invalid job ID: $jobId")
            return
        }
        val intent = Intent(this, JobDetailsActivity::class.java)
        intent.putExtra("jobId", jobId)
        startActivity(intent)
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
    override fun onResume() {
        super.onResume()
        notificationHelper.startJobNotificationsListener()
    }
    override fun onPause() {
        super.onPause()
        notificationHelper.stopJobNotificationsListener()
    }
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)

        // Add debug notification test option (only in debug builds)
        if (BuildConfig.DEBUG) {
            menu.add(0, 999, 100, "🧪 Test Notifications")
        }

        return true
    }
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_my_applications -> {
                startActivity(Intent(this, MyApplicationsActivity::class.java))
                true
            }
            R.id.action_notifications -> {
                startActivity(Intent(this, NotificationsActivity::class.java))
                true
            }
            R.id.action_saved_jobs -> {
                startActivity(Intent(this, SavedJobsActivity::class.java))
                true
            }
            R.id.action_messages -> {
                startActivity(Intent(this, ConversationsActivity::class.java))
                true
            }
            R.id.action_profile -> {
                startActivity(Intent(this, ProfileActivity::class.java))
                true
            }
            R.id.action_logout -> {
                logout()
                true
            }
            999 -> { // Test Notifications menu item
                showNotificationTestDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showNotificationTestDialog() {
        val options = arrayOf(
            "🔔 Test Job Notification",
            "📋 Test Application Status",
            "👁️ Test Profile View",
            "📄 Test CV Review",
            "💬 Test Message",
            "🧪 Test All Notifications",
            "🔍 Debug FCM Tokens",
            "💾 Force Save FCM Token",
            "🎯 Test Direct Notification"
        )

        AlertDialog.Builder(this)
            .setTitle("🧪 Notification Testing")
            .setItems(options) { _, which ->
                val notificationTester = NotificationTester(this)
                val currentUserId = auth.currentUser?.uid ?: "test_user_123"

                when (which) {
                    0 -> {
                        notificationTester.testJobNotification()
                        Toast.makeText(this, "Job notification sent!", Toast.LENGTH_SHORT).show()
                    }
                    1 -> {
                        notificationTester.testApplicationStatusNotification(currentUserId)
                        Toast.makeText(this, "Application status notification sent!", Toast.LENGTH_SHORT).show()
                    }
                    2 -> {
                        notificationTester.testProfileViewNotification(currentUserId)
                        Toast.makeText(this, "Profile view notification sent!", Toast.LENGTH_SHORT).show()
                    }
                    3 -> {
                        notificationTester.testCvReviewNotification(currentUserId)
                        Toast.makeText(this, "CV review notification sent!", Toast.LENGTH_SHORT).show()
                    }
                    4 -> {
                        notificationTester.testMessageNotification(currentUserId)
                        Toast.makeText(this, "Message notification sent!", Toast.LENGTH_SHORT).show()
                    }
                    5 -> {
                        notificationTester.testAllNotifications(currentUserId)
                        Toast.makeText(this, "All test notifications sent! Check your notification panel.", Toast.LENGTH_LONG).show()
                    }
                    6 -> {
                        // Debug FCM tokens
                        debugFcmTokens()
                    }
                    7 -> {
                        // Force save FCM token
                        forceSaveFcmToken()
                    }
                    8 -> {
                        // Test direct notification
                        testDirectNotification()
                    }
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun debugFcmTokens() {
        val currentUserId = auth.currentUser?.uid ?: return

        // Get current device token
        com.google.firebase.messaging.FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val currentToken = task.result
                Log.d("FCM_DEBUG", "🎫 Current device token: ${currentToken.take(20)}...")

                // Check what's saved in Firestore for this user
                db.collection("users").document(currentUserId).get()
                    .addOnSuccessListener { userDoc ->
                        val savedToken = userDoc.getString("fcmToken")
                        Log.d("FCM_DEBUG", "💾 Saved user token: ${savedToken?.take(20) ?: "NOT FOUND"}...")

                        val message = buildString {
                            append("🎫 Current Device Token: ${currentToken.take(20)}...\n")
                            append("💾 Saved User Token: ${savedToken?.take(20) ?: "NOT FOUND"}...\n")
                            append("✅ Match: ${currentToken == savedToken}")
                        }

                        AlertDialog.Builder(this)
                            .setTitle("🔍 FCM Token Debug")
                            .setMessage(message)
                            .setPositiveButton("OK", null)
                            .show()
                    }
                    .addOnFailureListener { e ->
                        Log.e("FCM_DEBUG", "Error checking user token", e)
                        Toast.makeText(this, "Error checking tokens: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
            } else {
                Log.e("FCM_DEBUG", "Failed to get current token", task.exception)
                Toast.makeText(this, "Failed to get current token", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun forceSaveFcmToken() {
        val currentUserId = auth.currentUser?.uid ?: return

        // Get current device token and force save it
        com.google.firebase.messaging.FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val currentToken = task.result
                Log.d("FCM_FORCE_SAVE", "🔄 Force saving token: ${currentToken.take(20)}...")

                // Save to users collection
                db.collection("users").document(currentUserId)
                    .update("fcmToken", currentToken)
                    .addOnSuccessListener {
                        Log.d("FCM_FORCE_SAVE", "✅ Token saved to users collection")
                        Toast.makeText(this, "✅ FCM Token saved to users collection", Toast.LENGTH_SHORT).show()

                        // Verify it was saved
                        debugFcmTokens()
                    }
                    .addOnFailureListener { e ->
                        Log.e("FCM_FORCE_SAVE", "❌ Failed to save to users, trying companies...", e)

                        // Try companies collection
                        db.collection("companies")
                            .whereEqualTo("userId", currentUserId)
                            .get()
                            .addOnSuccessListener { documents ->
                                if (!documents.isEmpty) {
                                    val companyDoc = documents.documents[0]
                                    companyDoc.reference.update("fcmToken", currentToken)
                                        .addOnSuccessListener {
                                            Log.d("FCM_FORCE_SAVE", "✅ Token saved to companies collection")
                                            Toast.makeText(this, "✅ FCM Token saved to companies collection", Toast.LENGTH_SHORT).show()
                                            debugFcmTokens()
                                        }
                                        .addOnFailureListener { e2 ->
                                            Log.e("FCM_FORCE_SAVE", "❌ Failed to save to companies", e2)
                                            Toast.makeText(this, "❌ Failed to save FCM token: ${e2.message}", Toast.LENGTH_LONG).show()
                                        }
                                } else {
                                    Log.e("FCM_FORCE_SAVE", "❌ No company document found")
                                    Toast.makeText(this, "❌ No user or company document found", Toast.LENGTH_LONG).show()
                                }
                            }
                            .addOnFailureListener { e2 ->
                                Log.e("FCM_FORCE_SAVE", "❌ Error checking companies", e2)
                                Toast.makeText(this, "❌ Error checking companies: ${e2.message}", Toast.LENGTH_LONG).show()
                            }
                    }
            } else {
                Log.e("FCM_FORCE_SAVE", "❌ Failed to get current token", task.exception)
                Toast.makeText(this, "❌ Failed to get current token", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun testDirectNotification() {
        val currentUserId = auth.currentUser?.uid ?: return

        // Get current device token and create a direct notification
        com.google.firebase.messaging.FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (task.isSuccessful) {
                val currentToken = task.result
                Log.d("DIRECT_TEST", "🎯 Testing direct notification with current token: ${currentToken.take(20)}...")

                // Create a test FCM message targeting this device
                val testMessage = org.json.JSONObject().apply {
                    put("to", currentToken)
                    put("data", org.json.JSONObject().apply {
                        put("type", "message")
                        put("title", "🧪 Direct Test Notification")
                        put("body", "This is a direct test to verify notification display works")
                    })
                    put("notification", org.json.JSONObject().apply {
                        put("title", "🧪 Direct Test Notification")
                        put("body", "This is a direct test to verify notification display works")
                        put("sound", "default")
                        put("priority", "high")
                    })
                }

                Log.d("DIRECT_TEST", "📤 Test message JSON: ${testMessage.toString()}")

                // Use NotificationManager to process this message
                val notificationManager = com.example.jobrec.services.NotificationManager(this)
                lifecycleScope.launch {
                    try {
                        // Call the private sendFcmMessage method via reflection or create a public test method
                        // For now, let's directly call showLocalNotification
                        val appContext = this@HomeActivity

                        Log.d("DIRECT_TEST", "🚀 Calling showLocalNotification directly...")

                        // Create a test notification directly
                        val notificationManagerSystem = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

                        val intent = Intent(this@HomeActivity, HomeActivity::class.java).apply {
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                        }

                        val pendingIntent = PendingIntent.getActivity(
                            this@HomeActivity,
                            System.currentTimeMillis().toInt(),
                            intent,
                            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                        )

                        val notification = NotificationCompat.Builder(this@HomeActivity, "message_notifications")
                            .setSmallIcon(R.drawable.ic_notification)
                            .setContentTitle("🧪 Direct Test Notification")
                            .setContentText("This is a direct test to verify notification display works")
                            .setAutoCancel(true)
                            .setContentIntent(pendingIntent)
                            .setPriority(NotificationCompat.PRIORITY_HIGH)
                            .setDefaults(NotificationCompat.DEFAULT_ALL)
                            .build()

                        val notificationId = System.currentTimeMillis().toInt()
                        Log.d("DIRECT_TEST", "🚀 Showing direct notification with ID: $notificationId")
                        notificationManagerSystem.notify(notificationId, notification)
                        Log.d("DIRECT_TEST", "✅ Direct notification should now be visible!")

                        Toast.makeText(this@HomeActivity, "🧪 Direct test notification sent! Check notification panel.", Toast.LENGTH_LONG).show()

                    } catch (e: Exception) {
                        Log.e("DIRECT_TEST", "❌ Error in direct test", e)
                        Toast.makeText(this@HomeActivity, "❌ Direct test failed: ${e.message}", Toast.LENGTH_LONG).show()
                    }
                }

            } else {
                Log.e("DIRECT_TEST", "❌ Failed to get current token", task.exception)
                Toast.makeText(this, "❌ Failed to get current token", Toast.LENGTH_SHORT).show()
            }
        }
    }
}