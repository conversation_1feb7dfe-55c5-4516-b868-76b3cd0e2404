<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:background="@drawable/dialog_background">

        <TextView
            android:id="@+id/dialogTitleTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Edit User"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:layout_marginBottom="16dp" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:hint="Name"
            app:startIconDrawable="@android:drawable/ic_menu_myplaces"
            app:startIconTint="#FF0000"
            app:boxStrokeColor="#FF0000"
            app:hintTextColor="#FF0000">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/nameEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:hint="Surname"
            app:startIconDrawable="@android:drawable/ic_menu_myplaces"
            app:startIconTint="#FF0000"
            app:boxStrokeColor="#FF0000"
            app:hintTextColor="#FF0000">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/surnameEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPersonName"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:hint="Email"
            app:startIconDrawable="@android:drawable/ic_dialog_email"
            app:startIconTint="#FF0000"
            app:boxStrokeColor="#FF0000"
            app:hintTextColor="#FF0000">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/emailEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/passwordLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:hint="Password (leave blank to keep unchanged)"
            app:startIconDrawable="@android:drawable/ic_lock_lock"
            app:startIconTint="#2196F3"
            app:boxStrokeColor="#2196F3"
            app:hintTextColor="#2196F3"
            app:passwordToggleEnabled="true"
            app:passwordToggleTint="#2196F3">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/passwordEditText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:textColor="@color/black" />
        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                app:backgroundTint="#2196F3"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/cancelButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cancel"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/saveButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Save Changes"
                app:backgroundTint="#2196F3" />
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
