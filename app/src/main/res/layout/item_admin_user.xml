<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/userNameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Name"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/userEmailText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Email"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginBottom="16dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/viewUserButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="View"
                android:textColor="@color/white"
                app:backgroundTint="@color/black"
                app:cornerRadius="4dp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/editUserButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Edit"
                android:textColor="@color/white"
                app:backgroundTint="@color/black"
                app:cornerRadius="4dp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/deleteUserButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Delete"
                android:textColor="@color/white"
                app:backgroundTint="@color/black"
                app:cornerRadius="4dp"
                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"/>
        </LinearLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>