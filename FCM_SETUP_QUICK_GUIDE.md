# 🚀 Quick FCM Setup Guide for Message Notifications

## Problem
Your message notifications aren't working because the FCM server key is missing.

## Solution (5 minutes)

### Step 1: Get Your FCM Server Key
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **careerworx-f5bc6**
3. Click the ⚙️ gear icon → **Project Settings**
4. Go to **Cloud Messaging** tab
5. Find **Server key** (starts with `AAAA...`)
6. Copy the entire key

### Step 2: Update the Code
1. Open `app/src/main/java/com/example/jobrec/services/NotificationManager.kt`
2. Find line 21: `private val FCM_SERVER_KEY = "AAAA_YOUR_ACTUAL_FCM_SERVER_KEY_HERE"`
3. Replace `AAAA_YOUR_ACTUAL_FCM_SERVER_KEY_HERE` with your actual key
4. Save the file

### Step 3: Test
1. Build and run the app on both devices
2. Send a message from emulator (company) to phone (student)
3. You should receive a notification on your phone!

## Example
```kotlin
// Before:
private val FCM_SERVER_KEY = "AAAA_YOUR_ACTUAL_FCM_SERVER_KEY_HERE"

// After (with your actual key):
private val FCM_SERVER_KEY = "AAAAabcd1234:APA91bF..."
```

## Troubleshooting

### If notifications still don't work:
1. **Check logs**: Look for "❌ FCM" errors in Android Studio logcat
2. **Check tokens**: Ensure FCM tokens are being saved to Firestore
3. **Check network**: Make sure both devices have internet
4. **Check permissions**: Ensure notification permissions are granted

### Common Issues:
- **Wrong server key**: Make sure you copied the full key from the correct Firebase project
- **Network blocked**: Some emulators block outgoing HTTP requests
- **Token not saved**: Check if FCM tokens are being saved to user/company documents in Firestore

## Quick Debug Commands
```bash
# Check if FCM tokens are saved in Firestore
# Go to Firebase Console → Firestore → users/companies collections
# Look for 'fcmToken' field in user documents
```

## Next Steps
Once this works, you can test:
- ✅ Message notifications
- ✅ Application status notifications  
- ✅ Job posting notifications
- ✅ Profile view notifications

---
**Need help?** Check the logs in Android Studio for specific error messages.
